import { CommonModule, NgIf } from '@angular/common';
import {
  Component,
  Input,
  Output,
  EventEmitter,
  ElementRef,
  TemplateRef,
  ViewChild,
  ViewEncapsulation
} from '@angular/core';

@Component({
  selector: 'ava-select-option',
  imports: [CommonModule, NgIf],
  templateUrl: './select-option.component.html',
  styleUrls: ['./select-option.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class SelectOptionComponent {
  @Input() value: any;
  @Input() selected = false;
  @Output() optionSelected = new EventEmitter<any>();
  @Input() visible: boolean = true;

  @ViewChild('optionTpl', { static: true }) templateRef!: TemplateRef<any>;
  @ViewChild('contentWrapper') contentWrapper!: ElementRef;

  constructor(public elementRef: ElementRef) { }
  get label(): string {
    return this.elementRef.nativeElement.textContent.trim().toLowerCase();
  }

  onClick() {
    this.optionSelected.emit(this.value);
  }
}

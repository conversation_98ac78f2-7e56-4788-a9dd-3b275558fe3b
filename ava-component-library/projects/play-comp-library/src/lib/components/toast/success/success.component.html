<div class="ava-toast ava-toast-success" 
     [style.width]="customWidth" 
     [style.height]="customHeight"
     (mouseenter)="onMouseEnter()" 
     (mouseleave)="onMouseLeave()">
  
  <!-- Icon -->
  <div class="ava-toast-icon">
    <ava-icon 
      [iconName]="icon || 'check-circle'" 
      [iconColor]="iconColor || 'white'" 
      [iconSize]="24">
    </ava-icon>
  </div>

  <!-- Content -->
  <div class="ava-toast-content">
    <div class="ava-toast-title" *ngIf="title">{{ title }}</div>
    <div class="ava-toast-message" *ngIf="message">{{ message }}</div>
    <ng-content></ng-content>
  </div>

  <!-- Close Button -->
  <button class="ava-toast-close" 
          *ngIf="showCloseButton" 
          (click)="onClose()"
          aria-label="Close toast">
    <ava-icon iconName="x" iconColor="white" [iconSize]="16"></ava-icon>
  </button>

  <!-- Progress Bar -->
  <div class="ava-toast-progress" 
       *ngIf="showProgress && duration && duration > 0"
       [style.animation-duration.ms]="duration"
       [style.animation-play-state]="isPaused ? 'paused' : 'running'">
  </div>
</div>

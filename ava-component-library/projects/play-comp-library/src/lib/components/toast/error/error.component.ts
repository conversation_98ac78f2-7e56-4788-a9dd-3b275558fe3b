import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../../icon/icon.component';
import { ButtonComponent } from '../../button/button.component';

@Component({
  selector: 'ava-toast-error',
  standalone: true,
  imports: [CommonModule, IconComponent, ButtonComponent],
  templateUrl: './error.component.html',
  styleUrls: ['./error.component.scss']
})
export class ErrorToastComponent implements OnInit, OnDestroy {
  @Input() title?: string;
  @Input() message?: string;
  @Input() duration = 4000;
  @Input() showCloseButton = true;
  @Input() showProgress = true;
  @Input() pauseOnHover = true;
  @Input() icon?: string;
  @Input() iconColor?: string;
  @Input() customWidth?: string;
  @Input() customHeight?: string;
  @Input() showRetryButton = false;
  @Input() retryButtonText?: string;

  @Output() closeToast = new EventEmitter<void>();
  @Output() retryAction = new EventEmitter<void>();

  isPaused = false;
  private timeoutId?: number;

  constructor(private elementRef: ElementRef) {}

  ngOnInit() {
    // Add show animation
    setTimeout(() => {
      this.elementRef.nativeElement.querySelector('.ava-toast')?.classList.add('ava-toast-show');
    }, 10);
  }

  ngOnDestroy() {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
    }
  }

  onClose() {
    this.closeToast.emit();
  }

  onRetry() {
    this.retryAction.emit();
  }

  onMouseEnter() {
    if (this.pauseOnHover) {
      this.isPaused = true;
    }
  }

  onMouseLeave() {
    if (this.pauseOnHover) {
      this.isPaused = false;
    }
  }
}

import { Injectable, signal, ApplicationRef, createComponent, EmbeddedViewRef, Injector, EnvironmentInjector, inject } from '@angular/core';
import { ToastContainerComponent } from './toast-container/toast-container.component';
import { SuccessToastComponent } from './success/success.component';
import { ErrorToastComponent } from './error/error.component';
import { WarningToastComponent } from './warning/warning.component';
import { InfoToastComponent } from './info/info.component';
import { CustomToastComponent } from './custom/custom.component';

export type ToastPosition = 
  | 'top-left' 
  | 'top-center' 
  | 'top-right' 
  | 'bottom-left' 
  | 'bottom-center' 
  | 'bottom-right';

export type ToastType = 'success' | 'error' | 'warning' | 'info' | 'custom';

export interface ToastConfig {
  title?: string;
  message?: string;
  duration?: number;
  position?: ToastPosition;
  showCloseButton?: boolean;
  showProgress?: boolean;
  pauseOnHover?: boolean;
  icon?: string;
  iconColor?: string;
  customWidth?: string;
  customHeight?: string;
}

export interface SuccessToastConfig extends ToastConfig {
  type?: 'success';
}

export interface ErrorToastConfig extends ToastConfig {
  type?: 'error';
  showRetryButton?: boolean;
}

export interface WarningToastConfig extends ToastConfig {
  type?: 'warning';
  showActionButton?: boolean;
  actionButtonText?: string;
}

export interface InfoToastConfig extends ToastConfig {
  type?: 'info';
  showLearnMoreButton?: boolean;
}

export interface CustomToastConfig extends ToastConfig {
  type?: 'custom';
  customWidth?: string;
  customHeight?: string;
  customContent?: string;
}

export interface ToastResult {
  action: 'close' | 'retry' | 'action' | 'learn-more' | 'timeout';
  data?: any;
}

@Injectable({
  providedIn: 'root'
})
export class ToastService {
  private appRef = inject(ApplicationRef);
  private injector = inject(Injector);
  private envInjector = inject(EnvironmentInjector);
  
  private toastContainerRef: any = null;
  private activeToasts = new Map<number, any>();
  private toastId = 0;
  private currentPosition: ToastPosition = 'top-right';

  private ensureContainer() {
    if (!this.toastContainerRef) {
      this.toastContainerRef = createComponent(ToastContainerComponent, {
        environmentInjector: this.envInjector,
        elementInjector: this.injector
      });

      this.appRef.attachView(this.toastContainerRef.hostView);
      const containerElem = (this.toastContainerRef.hostView as EmbeddedViewRef<any>)
        .rootNodes[0] as HTMLElement;
      document.body.appendChild(containerElem);
      
      // Set initial position
      this.toastContainerRef.instance.setPosition(this.currentPosition);
    }
  }

  private show<T extends object>(component: any, config?: Partial<T>): Promise<ToastResult> {
    this.ensureContainer();
    
    const id = ++this.toastId;
    
    return new Promise((resolve) => {
      // Create the toast component
      const toastRef = this.toastContainerRef.instance.container.createComponent(component, {
        injector: this.injector,
        environmentInjector: this.envInjector
      });

      // Set default config
      const defaultConfig = {
        duration: 4000,
        showCloseButton: true,
        showProgress: true,
        pauseOnHover: true,
        ...config
      };

      // Apply config to component
      if (config) {
        Object.assign(toastRef.instance, defaultConfig);
      }

      // Store the toast reference
      this.activeToasts.set(id, { ref: toastRef, resolve });

      // Set up event handlers
      if (toastRef.instance.closeToast) {
        toastRef.instance.closeToast.subscribe(() => {
          this.dismiss(id, { action: 'close' });
        });
      }

      if (toastRef.instance.retryAction) {
        toastRef.instance.retryAction.subscribe(() => {
          this.dismiss(id, { action: 'retry' });
        });
      }

      if (toastRef.instance.actionClick) {
        toastRef.instance.actionClick.subscribe(() => {
          this.dismiss(id, { action: 'action' });
        });
      }

      if (toastRef.instance.learnMoreClick) {
        toastRef.instance.learnMoreClick.subscribe(() => {
          this.dismiss(id, { action: 'learn-more' });
        });
      }

      // Auto dismiss if duration is set
      if (defaultConfig.duration && defaultConfig.duration > 0) {
        setTimeout(() => {
          if (this.activeToasts.has(id)) {
            this.dismiss(id, { action: 'timeout' });
          }
        }, defaultConfig.duration);
      }
    });
  }

  private dismiss(id: number, result: ToastResult) {
    const toast = this.activeToasts.get(id);
    if (toast) {
      // Add hide animation class
      const toastElement = toast.ref.location.nativeElement;
      toastElement.classList.add('toast-hide');
      
      setTimeout(() => {
        toast.ref.destroy();
        this.activeToasts.delete(id);
        toast.resolve(result);
        
        // Clean up container if no more toasts
        if (this.activeToasts.size === 0 && this.toastContainerRef) {
          this.toastContainerRef.destroy();
          this.toastContainerRef = null;
        }
      }, 400); // Animation duration
    }
  }

  success(config?: Partial<SuccessToastConfig>): Promise<ToastResult> {
    return this.show(SuccessToastComponent, {
      title: 'Success!',
      message: 'Operation completed successfully.',
      ...config
    });
  }

  error(config?: Partial<ErrorToastConfig>): Promise<ToastResult> {
    return this.show(ErrorToastComponent, {
      title: 'Error!',
      message: 'An error occurred. Please try again.',
      showRetryButton: false,
      ...config
    });
  }

  warning(config?: Partial<WarningToastConfig>): Promise<ToastResult> {
    return this.show(WarningToastComponent, {
      title: 'Warning!',
      message: 'Please review the following information carefully.',
      showActionButton: false,
      ...config
    });
  }

  info(config?: Partial<InfoToastConfig>): Promise<ToastResult> {
    return this.show(InfoToastComponent, {
      title: 'Information',
      message: 'Here is some important information for you.',
      showLearnMoreButton: false,
      ...config
    });
  }

  custom(config?: Partial<CustomToastConfig>): Promise<ToastResult> {
    return this.show(CustomToastComponent, {
      title: 'Notification',
      message: '',
      customWidth: '400px',
      customHeight: 'auto',
      ...config
    });
  }

  setPosition(position: ToastPosition) {
    this.currentPosition = position;
    if (this.toastContainerRef) {
      this.toastContainerRef.instance.setPosition(position);
    }
  }

  dismissAll() {
    const toastIds = Array.from(this.activeToasts.keys());
    toastIds.forEach(id => {
      this.dismiss(id, { action: 'close' });
    });
  }
}

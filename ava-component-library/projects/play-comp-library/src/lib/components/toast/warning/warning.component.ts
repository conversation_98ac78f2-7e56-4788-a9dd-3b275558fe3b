import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../../icon/icon.component';

@Component({
  selector: 'ava-toast-warning',
  standalone: true,
  imports: [CommonModule, IconComponent],
  template: `
    <div class="ava-toast ava-toast-warning" 
         [style.width]="customWidth" 
         [style.height]="customHeight"
         (mouseenter)="onMouseEnter()" 
         (mouseleave)="onMouseLeave()">
      
      <!-- Icon -->
      <div class="ava-toast-icon">
        <ava-icon 
          [iconName]="icon || 'alert-triangle'" 
          [iconColor]="iconColor || 'var(--global-color-black)'" 
          [iconSize]="24">
        </ava-icon>
      </div>

      <!-- Content -->
      <div class="ava-toast-content">
        <div class="ava-toast-title" *ngIf="title">{{ title }}</div>
        <div class="ava-toast-message" *ngIf="message">{{ message }}</div>
        <ng-content></ng-content>
        
        <!-- Action Button -->
        <div class="ava-toast-actions" *ngIf="showActionButton">
          <button class="ava-toast-action" (click)="onAction()">
            {{ actionButtonText || 'Take Action' }}
          </button>
        </div>
      </div>

      <!-- Close Button -->
      <button class="ava-toast-close" 
              *ngIf="showCloseButton" 
              (click)="onClose()"
              aria-label="Close toast">
        <ava-icon iconName="x" iconColor="var(--global-color-black)" [iconSize]="16"></ava-icon>
      </button>

      <!-- Progress Bar -->
      <div class="ava-toast-progress" 
           *ngIf="showProgress && duration && duration > 0"
           [style.animation-duration.ms]="duration"
           [style.animation-play-state]="isPaused ? 'paused' : 'running'">
      </div>
    </div>
  `,
  styleUrls: ['../toast-base.scss']
})
export class WarningToastComponent implements OnInit, OnDestroy {
  @Input() title?: string;
  @Input() message?: string;
  @Input() duration = 4000;
  @Input() showCloseButton = true;
  @Input() showProgress = true;
  @Input() pauseOnHover = true;
  @Input() icon?: string;
  @Input() iconColor?: string;
  @Input() customWidth?: string;
  @Input() customHeight?: string;
  @Input() showActionButton = false;
  @Input() actionButtonText?: string;

  @Output() closeToast = new EventEmitter<void>();
  @Output() actionClick = new EventEmitter<void>();

  isPaused = false;
  private timeoutId?: number;

  constructor(private elementRef: ElementRef) {}

  ngOnInit() {
    // Add show animation
    setTimeout(() => {
      this.elementRef.nativeElement.querySelector('.ava-toast')?.classList.add('ava-toast-show');
    }, 10);
  }

  ngOnDestroy() {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
    }
  }

  onClose() {
    this.closeToast.emit();
  }

  onAction() {
    this.actionClick.emit();
  }

  onMouseEnter() {
    if (this.pauseOnHover) {
      this.isPaused = true;
    }
  }

  onMouseLeave() {
    if (this.pauseOnHover) {
      this.isPaused = false;
    }
  }
}

import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../../icon/icon.component';

@Component({
  selector: 'ava-toast-info',
  standalone: true,
  imports: [CommonModule, IconComponent],
  template: `
    <div class="ava-toast ava-toast-info" 
         [style.width]="customWidth" 
         [style.height]="customHeight"
         (mouseenter)="onMouseEnter()" 
         (mouseleave)="onMouseLeave()">
      
      <!-- Icon -->
      <div class="ava-toast-icon">
        <ava-icon 
          [iconName]="icon || 'info'" 
          [iconColor]="iconColor || 'white'" 
          [iconSize]="24">
        </ava-icon>
      </div>

      <!-- Content -->
      <div class="ava-toast-content">
        <div class="ava-toast-title" *ngIf="title">{{ title }}</div>
        <div class="ava-toast-message" *ngIf="message">{{ message }}</div>
        <ng-content></ng-content>
        
        <!-- Learn More Button -->
        <div class="ava-toast-actions" *ngIf="showLearnMoreButton">
          <button class="ava-toast-action" (click)="onLearnMore()">
            {{ learnMoreButtonText || 'Learn More' }}
          </button>
        </div>
      </div>

      <!-- Close Button -->
      <button class="ava-toast-close" 
              *ngIf="showCloseButton" 
              (click)="onClose()"
              aria-label="Close toast">
        <ava-icon iconName="x" iconColor="white" [iconSize]="16"></ava-icon>
      </button>

      <!-- Progress Bar -->
      <div class="ava-toast-progress" 
           *ngIf="showProgress && duration && duration > 0"
           [style.animation-duration.ms]="duration"
           [style.animation-play-state]="isPaused ? 'paused' : 'running'">
      </div>
    </div>
  `,
  styleUrls: ['../toast-base.scss']
})
export class InfoToastComponent implements OnInit, OnDestroy {
  @Input() title?: string;
  @Input() message?: string;
  @Input() duration = 4000;
  @Input() showCloseButton = true;
  @Input() showProgress = true;
  @Input() pauseOnHover = true;
  @Input() icon?: string;
  @Input() iconColor?: string;
  @Input() customWidth?: string;
  @Input() customHeight?: string;
  @Input() showLearnMoreButton = false;
  @Input() learnMoreButtonText?: string;

  @Output() closeToast = new EventEmitter<void>();
  @Output() learnMoreClick = new EventEmitter<void>();

  isPaused = false;
  private timeoutId?: number;

  constructor(private elementRef: ElementRef) {}

  ngOnInit() {
    // Add show animation
    setTimeout(() => {
      this.elementRef.nativeElement.querySelector('.ava-toast')?.classList.add('ava-toast-show');
    }, 10);
  }

  ngOnDestroy() {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
    }
  }

  onClose() {
    this.closeToast.emit();
  }

  onLearnMore() {
    this.learnMoreClick.emit();
  }

  onMouseEnter() {
    if (this.pauseOnHover) {
      this.isPaused = true;
    }
  }

  onMouseLeave() {
    if (this.pauseOnHover) {
      this.isPaused = false;
    }
  }
}

<div class="documentation">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <header class="doc-header">
        <h1>Toast Component</h1>
        <p class="description">
          A flexible and powerful toast notification system with multiple types, positions, and customization options. 
          Built with service pattern architecture for easy integration and management.
        </p>
      </header>
    </div>
  </div>

  <!-- Installation -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Installation</h2>
        <div class="code-block">
          <pre><code>import {{ '{' }} ToastService {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
        </div>
      </section>
    </div>
  </div>

  <!-- Controls -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Interactive Demo</h2>
        <div class="demo-container">
          
          <!-- Basic Toast Types -->
          <div class="control-group">
            <h3>Toast Types</h3>
            <div class="control-row">
              <button class="btn btn-success" (click)="showSuccessToast()">Success</button>
              <button class="btn btn-error" (click)="showErrorToast()">Error</button>
              <button class="btn btn-warning" (click)="showWarningToast()">Warning</button>
              <button class="btn btn-info" (click)="showInfoToast()">Info</button>
            </div>
          </div>

          <!-- Position Control -->
          <div class="control-group">
            <h3>Position</h3>
            <select [(ngModel)]="selectedPosition" (change)="updatePosition()" class="form-control">
              <option *ngFor="let pos of positions" [value]="pos.value">{{ pos.label }}</option>
            </select>
            <div class="control-row" style="margin-top: 10px;">
              <button *ngFor="let pos of positions" 
                      class="btn btn-outline" 
                      (click)="showPositionExample(pos.value)">
                {{ pos.label }}
              </button>
            </div>
          </div>

          <!-- Duration Control -->
          <div class="control-group">
            <h3>Duration (ms)</h3>
            <input type="number" 
                   [(ngModel)]="duration" 
                   min="1000" 
                   max="10000" 
                   step="500" 
                   class="form-control">
          </div>

          <!-- Custom Toast -->
          <div class="control-group">
            <h3>Custom Toast</h3>
            <input type="text" 
                   [(ngModel)]="customTitle" 
                   placeholder="Enter title..." 
                   class="form-control" 
                   style="margin-bottom: 10px;">
            <input type="text" 
                   [(ngModel)]="customMessage" 
                   placeholder="Enter message..." 
                   class="form-control" 
                   style="margin-bottom: 10px;">
            <div class="control-row">
              <input type="text" 
                     [(ngModel)]="customWidth" 
                     placeholder="Width (e.g., 400px)" 
                     class="form-control">
              <input type="text" 
                     [(ngModel)]="customHeight" 
                     placeholder="Height (e.g., auto)" 
                     class="form-control">
            </div>
            <div class="control-row" style="margin-top: 10px;">
              <button class="btn btn-primary" (click)="showCustomToast()">Show Custom</button>
              <button class="btn btn-secondary" (click)="showCustomContentToast()">Custom Content</button>
            </div>
          </div>

          <!-- Actions -->
          <div class="control-group">
            <h3>Actions</h3>
            <button class="btn btn-danger" (click)="dismissAllToasts()">Dismiss All</button>
          </div>

        </div>
      </section>
    </div>
  </div>

  <!-- API Documentation -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>API Reference</h2>
        
        <h3>ToastService Methods</h3>
        <table class="api-table">
          <thead>
            <tr>
              <th>Method</th>
              <th>Parameters</th>
              <th>Return Type</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><code>success(config?)</code></td>
              <td><code>SuccessToastConfig</code></td>
              <td><code>Promise&lt;ToastResult&gt;</code></td>
              <td>Show a success toast notification</td>
            </tr>
            <tr>
              <td><code>error(config?)</code></td>
              <td><code>ErrorToastConfig</code></td>
              <td><code>Promise&lt;ToastResult&gt;</code></td>
              <td>Show an error toast notification</td>
            </tr>
            <tr>
              <td><code>warning(config?)</code></td>
              <td><code>WarningToastConfig</code></td>
              <td><code>Promise&lt;ToastResult&gt;</code></td>
              <td>Show a warning toast notification</td>
            </tr>
            <tr>
              <td><code>info(config?)</code></td>
              <td><code>InfoToastConfig</code></td>
              <td><code>Promise&lt;ToastResult&gt;</code></td>
              <td>Show an info toast notification</td>
            </tr>
            <tr>
              <td><code>custom(config?)</code></td>
              <td><code>CustomToastConfig</code></td>
              <td><code>Promise&lt;ToastResult&gt;</code></td>
              <td>Show a custom toast notification</td>
            </tr>
            <tr>
              <td><code>setPosition(position)</code></td>
              <td><code>ToastPosition</code></td>
              <td><code>void</code></td>
              <td>Set the global toast position</td>
            </tr>
            <tr>
              <td><code>dismissAll()</code></td>
              <td>-</td>
              <td><code>void</code></td>
              <td>Dismiss all active toasts</td>
            </tr>
          </tbody>
        </table>

        <h3>Configuration Options</h3>
        <table class="api-table">
          <thead>
            <tr>
              <th>Property</th>
              <th>Type</th>
              <th>Default</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><code>title</code></td>
              <td><code>string</code></td>
              <td><code>undefined</code></td>
              <td>Toast title text</td>
            </tr>
            <tr>
              <td><code>message</code></td>
              <td><code>string</code></td>
              <td><code>undefined</code></td>
              <td>Toast message text</td>
            </tr>
            <tr>
              <td><code>duration</code></td>
              <td><code>number</code></td>
              <td><code>4000</code></td>
              <td>Auto-dismiss duration in milliseconds</td>
            </tr>
            <tr>
              <td><code>position</code></td>
              <td><code>ToastPosition</code></td>
              <td><code>'top-right'</code></td>
              <td>Toast position on screen</td>
            </tr>
            <tr>
              <td><code>showCloseButton</code></td>
              <td><code>boolean</code></td>
              <td><code>true</code></td>
              <td>Show close button</td>
            </tr>
            <tr>
              <td><code>showProgress</code></td>
              <td><code>boolean</code></td>
              <td><code>true</code></td>
              <td>Show progress bar</td>
            </tr>
            <tr>
              <td><code>pauseOnHover</code></td>
              <td><code>boolean</code></td>
              <td><code>true</code></td>
              <td>Pause auto-dismiss on hover</td>
            </tr>
            <tr>
              <td><code>icon</code></td>
              <td><code>string</code></td>
              <td><code>auto</code></td>
              <td>Custom icon name (Lucide icons)</td>
            </tr>
            <tr>
              <td><code>iconColor</code></td>
              <td><code>string</code></td>
              <td><code>auto</code></td>
              <td>Custom icon color</td>
            </tr>
            <tr>
              <td><code>customWidth</code></td>
              <td><code>string</code></td>
              <td><code>'400px'</code></td>
              <td>Custom toast width (custom type only)</td>
            </tr>
            <tr>
              <td><code>customHeight</code></td>
              <td><code>string</code></td>
              <td><code>'auto'</code></td>
              <td>Custom toast height (custom type only)</td>
            </tr>
          </tbody>
        </table>
      </section>
    </div>
  </div>

  <!-- Examples -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Examples</h2>
        
        <div *ngFor="let section of sections" class="example-section">
          <div class="section-header" (click)="toggleCode(section)">
            <h3>{{ section.title }}</h3>
            <p>{{ section.description }}</p>
            <button class="toggle-code-btn">
              {{ section.showCode ? 'Hide Code' : 'Show Code' }}
            </button>
          </div>

          <!-- Code Example -->
          <div class="code-example" [class.expanded]="section.showCode">
            <div class="code-block" *ngIf="section.showCode">
              <div class="code-content">
                <pre><code [innerText]="getToastCode(section.title.toLowerCase())"></code></pre>
              </div>
              <button class="copy-button" (click)="copyCode(section.title.toLowerCase())">
                Copy
              </button>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</div>

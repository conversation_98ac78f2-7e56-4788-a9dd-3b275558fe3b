import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ToastService, ToastPosition } from '../../../../../play-comp-library/src/lib/components/toast';

@Component({
  selector: 'app-toast-documentation',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './app-toast.component.html',
  styleUrls: ['./app-toast.component.scss']
})
export class ToastDocumentationComponent implements OnInit {
  
  // Form controls
  customTitle = '';
  customMessage = '';
  customWidth = '400px';
  customHeight = 'auto';
  selectedPosition: ToastPosition = 'top-right';
  duration = 4000;

  positions: { value: ToastPosition; label: string }[] = [
    { value: 'top-left', label: 'Top Left' },
    { value: 'top-center', label: 'Top Center' },
    { value: 'top-right', label: 'Top Right' },
    { value: 'bottom-left', label: 'Bottom Left' },
    { value: 'bottom-center', label: 'Bottom Center' },
    { value: 'bottom-right', label: 'Bottom Right' }
  ];

  sections = [
    {
      title: 'Success Toast',
      description: 'Display success notifications with check icon.',
      showCode: false,
    },
    {
      title: 'Error Toast',
      description: 'Display error notifications with alert icon and optional retry button.',
      showCode: false,
    },
    {
      title: 'Warning Toast',
      description: 'Display warning notifications with triangle icon.',
      showCode: false,
    },
    {
      title: 'Info Toast',
      description: 'Display informational notifications with info icon.',
      showCode: false,
    },
    {
      title: 'Custom Toast',
      description: 'Fully customizable toast with custom dimensions and content.',
      showCode: false,
    },
    {
      title: 'Position Examples',
      description: 'Toast positioning in different screen locations.',
      showCode: false,
    }
  ];

  constructor(private toastService: ToastService) {}

  ngOnInit() {
    // Welcome toast
    setTimeout(() => {
      this.toastService.info({
        title: 'Welcome! 👋',
        message: 'Try out the different toast options below.',
        duration: 5000
      });
    }, 1000);
  }

  // Basic toast methods
  showSuccessToast() {
    this.toastService.success({
      title: 'Success!',
      message: 'Operation completed successfully.',
      duration: this.duration
    });
  }

  showErrorToast() {
    this.toastService.error({
      title: 'Error!',
      message: 'Something went wrong. Please try again.',
      showRetryButton: true,
      duration: this.duration
    }).then(result => {
      if (result.action === 'retry') {
        this.toastService.success({
          title: 'Retrying...',
          message: 'Operation is being retried.',
          duration: 2000
        });
      }
    });
  }

  showWarningToast() {
    this.toastService.warning({
      title: 'Warning!',
      message: 'Please review your input carefully.',
      showActionButton: true,
      actionButtonText: 'Review',
      duration: this.duration
    }).then(result => {
      if (result.action === 'action') {
        this.toastService.info({
          title: 'Reviewing...',
          message: 'Taking you to review page.',
          duration: 2000
        });
      }
    });
  }

  showInfoToast() {
    this.toastService.info({
      title: 'Information',
      message: 'Here is some important information for you.',
      showLearnMoreButton: true,
      duration: this.duration
    }).then(result => {
      if (result.action === 'learn-more') {
        this.toastService.success({
          title: 'Learn More',
          message: 'Opening documentation...',
          duration: 2000
        });
      }
    });
  }

  showCustomToast() {
    if (!this.customTitle && !this.customMessage) {
      this.toastService.warning({
        title: 'Warning',
        message: 'Please enter a title or message for the custom toast.',
        duration: 3000
      });
      return;
    }

    this.toastService.custom({
      title: this.customTitle || undefined,
      message: this.customMessage || undefined,
      customWidth: this.customWidth,
      customHeight: this.customHeight,
      icon: 'star',
      duration: this.duration
    });
  }

  showCustomContentToast() {
    this.toastService.custom({
      title: 'Custom Content',
      customContent: `
        <div style="margin-top: 8px;">
          <p style="margin: 4px 0; font-size: 14px;">This is custom HTML content!</p>
          <ul style="margin: 8px 0; padding-left: 16px; font-size: 13px;">
            <li>Feature 1 enabled</li>
            <li>Feature 2 updated</li>
            <li>Feature 3 added</li>
          </ul>
        </div>
      `,
      customWidth: '450px',
      duration: this.duration
    });
  }

  // Position examples
  showPositionExample(position: ToastPosition) {
    this.toastService.setPosition(position);
    this.toastService.success({
      title: `Toast at ${position}`,
      message: `This toast is positioned at ${position.replace('-', ' ')}.`,
      duration: 3000
    });
  }

  // Utility methods
  updatePosition() {
    this.toastService.setPosition(this.selectedPosition);
  }

  dismissAllToasts() {
    this.toastService.dismissAll();
  }

  toggleCode(section: any) {
    section.showCode = !section.showCode;
  }

  getToastCode(sectionTitle: string): string {
    const examples: Record<string, string> = {
      'success toast': `
import { Component } from '@angular/core';
import { ToastService } from '@awe/play-comp-library';

@Component({
  selector: 'app-example',
  template: \`
    <button (click)="showSuccess()">Show Success Toast</button>
  \`
})
export class ExampleComponent {
  constructor(private toastService: ToastService) {}

  showSuccess() {
    this.toastService.success({
      title: 'Success!',
      message: 'Operation completed successfully.',
      duration: 4000
    });
  }
}`,
      'error toast': `
import { Component } from '@angular/core';
import { ToastService } from '@awe/play-comp-library';

@Component({
  selector: 'app-example',
  template: \`
    <button (click)="showError()">Show Error Toast</button>
  \`
})
export class ExampleComponent {
  constructor(private toastService: ToastService) {}

  showError() {
    this.toastService.error({
      title: 'Error!',
      message: 'Something went wrong.',
      showRetryButton: true,
      duration: 4000
    }).then(result => {
      if (result.action === 'retry') {
        // Handle retry action
        console.log('User clicked retry');
      }
    });
  }
}`,
      'warning toast': `
import { Component } from '@angular/core';
import { ToastService } from '@awe/play-comp-library';

@Component({
  selector: 'app-example',
  template: \`
    <button (click)="showWarning()">Show Warning Toast</button>
  \`
})
export class ExampleComponent {
  constructor(private toastService: ToastService) {}

  showWarning() {
    this.toastService.warning({
      title: 'Warning!',
      message: 'Please review your input.',
      showActionButton: true,
      actionButtonText: 'Review',
      duration: 4000
    });
  }
}`,
      'info toast': `
import { Component } from '@angular/core';
import { ToastService } from '@awe/play-comp-library';

@Component({
  selector: 'app-example',
  template: \`
    <button (click)="showInfo()">Show Info Toast</button>
  \`
})
export class ExampleComponent {
  constructor(private toastService: ToastService) {}

  showInfo() {
    this.toastService.info({
      title: 'Information',
      message: 'Here is some important info.',
      showLearnMoreButton: true,
      duration: 4000
    });
  }
}`,
      'custom toast': `
import { Component } from '@angular/core';
import { ToastService } from '@awe/play-comp-library';

@Component({
  selector: 'app-example',
  template: \`
    <button (click)="showCustom()">Show Custom Toast</button>
  \`
})
export class ExampleComponent {
  constructor(private toastService: ToastService) {}

  showCustom() {
    this.toastService.custom({
      title: 'Custom Toast',
      message: 'This is a custom toast.',
      customWidth: '500px',
      customHeight: '120px',
      icon: 'star',
      duration: 4000
    });
  }
}`,
      'position examples': `
import { Component } from '@angular/core';
import { ToastService } from '@awe/play-comp-library';

@Component({
  selector: 'app-example',
  template: \`
    <button (click)="showAtTopRight()">Top Right</button>
    <button (click)="showAtBottomLeft()">Bottom Left</button>
  \`
})
export class ExampleComponent {
  constructor(private toastService: ToastService) {}

  showAtTopRight() {
    this.toastService.setPosition('top-right');
    this.toastService.success({
      title: 'Top Right Toast',
      message: 'Positioned at top right.',
      duration: 3000
    });
  }

  showAtBottomLeft() {
    this.toastService.setPosition('bottom-left');
    this.toastService.info({
      title: 'Bottom Left Toast',
      message: 'Positioned at bottom left.',
      duration: 3000
    });
  }
}`
    };

    return examples[sectionTitle.toLowerCase()] || '';
  }

  copyCode(sectionTitle: string) {
    const code = this.getToastCode(sectionTitle);
    navigator.clipboard.writeText(code).then(() => {
      this.toastService.success({
        title: 'Copied!',
        message: 'Code copied to clipboard.',
        duration: 2000
      });
    });
  }
}
